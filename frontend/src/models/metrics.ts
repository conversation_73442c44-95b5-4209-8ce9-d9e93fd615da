/**
 * ----------  DAS<PERSON><PERSON>ARD METRICS TYPES  ----------
 */

/* 1. Sous-affranchissement par expéditeur */
export interface ExpediteurSousAffranchissement {
  nom: string
  adresse: string
  coclico: string | null
  siret: string | null
  site: string | null
  plis: number
  sous_aff: number | null
  cas_a_deliver: number | null
  cas_b_collect_ttc: number | null
  cas_c_shipping_ttc: number | null
}

/* 2. Quantité d’enveloppes finalisées par jour */
export interface EnveloppesParJour {
  jour: string // ISO date "YYYY-MM-DD"
  nombre: number
}

/* 3. Productivité utilisateur */
export interface TempsMoyenUtilisateur {
  user_id: number
  user_email: string
  nombre_plis: number
  moyenne_plis_par_jour: number
}

/* 4. Top-10 expéditeurs par gains potentiels */
export interface TopExpediteur {
  nom: string
  cas_c_shipping_ttc: number | null
  sous_aff: number | null
  volume?: number
  moyenne_cas_c?: number
}

/* 5. <PERSON><PERSON><PERSON> des gains par site et expéditeur */
export interface GainsParSite {
  site: string
  expediteur: string | null
  sous_aff: number | null
  cas_a_deliver: number | null
  cas_b_collect_ttc: number | null
  cas_c_shipping_ttc: number | null
  volume?: number
  moyenne_sous_aff?: number
}

/* 6. Durée moyenne de saisie d'un pli */
export interface DureeMoyenneSaisie {
  duree_moyenne_minutes: number | null
  nombre_enveloppes_terminees: number
  duree_min_minutes: number | null
  duree_max_minutes: number | null
}

/* 7. Objet racine retourné par l’API */
export interface DashboardMetricsResponse {
  expediteurs_sous_affranchissement: ExpediteurSousAffranchissement[]
  enveloppes_par_jour: EnveloppesParJour[]
  temps_moyen_utilisateur: TempsMoyenUtilisateur[]
  top10_expediteurs: TopExpediteur[]
  gains_par_site: GainsParSite[]
  duree_moyenne_saisie: DureeMoyenneSaisie | null
}
